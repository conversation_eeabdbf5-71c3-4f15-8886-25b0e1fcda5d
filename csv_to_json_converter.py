#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV to JSON Converter
将分层CSV文件转换为嵌套JSON格式
"""

import csv
import json
import re
from typing import Dict, List, Optional, Any
from pypinyin import lazy_pinyin, Style


def chinese_to_pinyin_code(text: str, parent_code: str = "") -> str:
    """
    将中文转换为拼音首字母大写的代码

    Args:
        text: 中文文本
        parent_code: 父级代码

    Returns:
        生成的代码
    """
    if not text or text.strip() == "":
        return parent_code

    # 清理文本，移除特殊字符和换行符
    clean_text = re.sub(r'[^\u4e00-\u9fff\w]', '', text.strip())

    if not clean_text:
        return parent_code

    # 获取拼音首字母
    pinyin_list = lazy_pinyin(clean_text, style=Style.FIRST_LETTER)
    code_suffix = ''.join([py.upper() for py in pinyin_list if py.isalpha()])

    # 组合父级代码和当前代码
    if parent_code:
        return parent_code + code_suffix
    else:
        return code_suffix


def create_hierarchy_node(name: str, code: str, parent_node: Optional[Dict] = None) -> Dict[str, Any]:
    """
    创建层级节点

    Args:
        name: 节点名称
        code: 节点代码
        parent_node: 父节点（完整的父节点对象）

    Returns:
        节点字典
    """
    node = {
        "code": code,
        "name": name
    }

    if parent_node:
        node["parent"] = parent_node

    return node


def process_csv_to_json(csv_file_path: str, output_file_path: str = None) -> List[Dict[str, Any]]:
    """
    处理CSV文件并转换为JSON格式

    Args:
        csv_file_path: CSV文件路径
        output_file_path: 输出JSON文件路径（可选）

    Returns:
        转换后的JSON数据列表
    """
    result = []  # 最终结果列表
    current_hierarchy = ["", "", "", "", ""]  # 当前层级状态

    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)

        # 跳过标题行
        next(reader, None)

        for row in reader:
            if len(row) < 5:
                continue

            # 解析每一行的层级数据
            level_data = []
            for i in range(5):
                if i < len(row):
                    cell = row[i].strip()
                    level_data.append(cell)
                else:
                    level_data.append("")

            # 更新当前层级状态（继承机制）
            for i in range(5):
                if level_data[i]:
                    current_hierarchy[i] = level_data[i]
                    # 当某一级有新值时，清空后面的级别
                    for j in range(i + 1, 5):
                        if not level_data[j]:
                            current_hierarchy[j] = ""

            # 找到最深的非空层级
            deepest_level = -1
            for i in range(4, -1, -1):
                if current_hierarchy[i]:
                    deepest_level = i
                    break

            if deepest_level == -1:
                continue  # 跳过空行

            # 构建完整的层级路径
            path = []
            codes = []
            parent_code = ""

            for i in range(deepest_level + 1):
                if current_hierarchy[i]:
                    path.append(current_hierarchy[i])
                    current_code = chinese_to_pinyin_code(current_hierarchy[i], parent_code)
                    codes.append(current_code)
                    parent_code = current_code

            if not path:
                continue

            # 构建嵌套结构（从最深层向上构建）
            current_node = None
            for i in range(len(path) - 1, -1, -1):
                if i == len(path) - 1:
                    # 最深层节点（叶子节点）
                    current_node = create_hierarchy_node(path[i], codes[i])
                else:
                    # 父级节点
                    current_node = create_hierarchy_node(path[i], codes[i], current_node)

            # 只添加叶子节点到结果中
            if current_node and len(path) > 0:
                # 检查是否已经存在相同的节点
                existing = None
                for item in result:
                    if (item["name"] == path[-1] and
                        item["code"] == codes[-1]):
                        existing = item
                        break

                if not existing:
                    # 添加最深层的节点（包含完整的父级链）
                    leaf_node = create_hierarchy_node(path[-1], codes[-1])
                    if len(path) > 1:
                        # 构建父级链
                        parent_chain = None
                        for i in range(len(path) - 2, -1, -1):
                            if parent_chain is None:
                                parent_chain = create_hierarchy_node(path[i], codes[i])
                            else:
                                parent_chain = create_hierarchy_node(path[i], codes[i], parent_chain)
                        leaf_node["parent"] = parent_chain

                    result.append(leaf_node)

    # 如果指定了输出文件路径，保存到文件
    if output_file_path:
        with open(output_file_path, 'w', encoding='utf-8') as output_file:
            json.dump(result, output_file, ensure_ascii=False, indent=2)
        print(f"JSON数据已保存到: {output_file_path}")

    return result


def main():
    """主函数"""
    csv_file = "/Users/<USER>/Desktop/classify/classify.csv"
    output_file = "/Users/<USER>/Desktop/classify/classify.json"
    
    try:
        print("开始转换CSV到JSON...")
        result = process_csv_to_json(csv_file, output_file)
        
        print(f"转换完成！共生成 {len(result)} 个顶级节点")
        
        # 打印前几个节点作为示例
        print("\n示例数据:")
        for i, node in enumerate(result[:3]):
            print(f"节点 {i+1}:")
            print(json.dumps(node, ensure_ascii=False, indent=2))
            print("-" * 50)
            
    except Exception as e:
        print(f"转换过程中出现错误: {e}")


if __name__ == "__main__":
    main()
