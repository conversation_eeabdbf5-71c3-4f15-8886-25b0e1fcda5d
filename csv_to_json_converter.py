#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV to JSON Converter
将分层CSV文件转换为嵌套JSON格式
"""

import csv
import json
import re
from typing import Dict, List, Optional, Any
from pypinyin import lazy_pinyin, Style


def chinese_to_pinyin_code(text: str, parent_code: str = "") -> str:
    """
    将中文转换为拼音首字母大写的代码

    Args:
        text: 中文文本
        parent_code: 父级代码

    Returns:
        生成的代码
    """
    if not text or text.strip() == "":
        return parent_code

    # 清理文本，将特定符号替换为下划线，移除换行符等特殊字符
    clean_text = text.strip()
    # 左括号替换为下划线，右括号直接移除
    clean_text = re.sub(r'[（\(]', '_', clean_text)  # 左括号转下划线
    clean_text = re.sub(r'[）\)]', '', clean_text)   # 右括号直接移除
    # 移除换行符和其他空白字符
    clean_text = re.sub(r'[\n\r\t\s]+', '', clean_text)
    # 移除其他特殊符号但保留中文字符、字母、数字、下划线和常见标点
    clean_text = re.sub(r'[^\u4e00-\u9fff\w_、，。！？；：""'']', '', clean_text)

    if not clean_text:
        return parent_code

    # 获取拼音首字母，保留下划线
    result_chars = []
    for char in clean_text:
        if char == '_':
            result_chars.append('_')
        elif '\u4e00' <= char <= '\u9fff':  # 中文字符
            pinyin = lazy_pinyin(char, style=Style.FIRST_LETTER)
            if pinyin and pinyin[0].isalpha():
                result_chars.append(pinyin[0].upper())
        elif char.isalpha():  # 英文字符
            result_chars.append(char.upper())
        elif char.isdigit():  # 数字
            result_chars.append(char)

    code_suffix = ''.join(result_chars)

    # 组合父级代码和当前代码，使用 - 连接
    if parent_code:
        return parent_code + '-' + code_suffix
    else:
        return code_suffix


def clean_name(name: str) -> str:
    """
    清理名称，去掉换行符等特殊字符

    Args:
        name: 原始名称

    Returns:
        清理后的名称
    """
    if not name:
        return name

    # 去掉换行符、制表符等特殊字符，但保留正常的标点符号
    cleaned = re.sub(r'[\n\r\t]+', '', name.strip())
    # 去掉多余的空格
    cleaned = re.sub(r'\s+', ' ', cleaned)

    return cleaned


def create_hierarchy_node(name: str, code: str, children: Optional[List[Dict]] = None) -> Dict[str, Any]:
    """
    创建层级节点

    Args:
        name: 节点名称
        code: 节点代码
        children: 子节点列表

    Returns:
        节点字典
    """
    node = {
        "code": code,
        "name": clean_name(name)
    }

    if children:
        node["children"] = children

    return node


def process_csv_to_json(csv_file_path: str, output_file_path: str = None) -> List[Dict[str, Any]]:
    """
    处理CSV文件并转换为JSON格式（children数组格式）

    Args:
        csv_file_path: CSV文件路径
        output_file_path: 输出JSON文件路径（可选）

    Returns:
        转换后的JSON数据列表
    """
    # 用于存储所有路径的字典，键为路径，值为节点
    all_paths = {}
    current_hierarchy = ["", "", "", "", ""]  # 当前层级状态

    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)

        # 跳过标题行
        next(reader, None)

        for row in reader:
            if len(row) < 5:
                continue

            # 解析每一行的层级数据
            level_data = []
            for i in range(5):
                if i < len(row):
                    cell = row[i].strip()
                    level_data.append(cell)
                else:
                    level_data.append("")

            # 更新当前层级状态（继承机制）
            for i in range(5):
                if level_data[i]:
                    current_hierarchy[i] = level_data[i]
                    # 当某一级有新值时，清空后面的级别
                    for j in range(i + 1, 5):
                        if not level_data[j]:
                            current_hierarchy[j] = ""

            # 找到最深的非空层级
            deepest_level = -1
            for i in range(4, -1, -1):
                if current_hierarchy[i]:
                    deepest_level = i
                    break

            if deepest_level == -1:
                continue  # 跳过空行

            # 构建完整的层级路径
            path = []
            codes = []
            parent_code = ""

            for i in range(deepest_level + 1):
                if current_hierarchy[i]:
                    path.append(current_hierarchy[i])
                    current_code = chinese_to_pinyin_code(current_hierarchy[i], parent_code)
                    codes.append(current_code)
                    parent_code = current_code

            if not path:
                continue

            # 为每个层级创建或更新节点
            for level in range(len(path)):
                current_path = " -> ".join(path[:level + 1])

                if current_path not in all_paths:
                    all_paths[current_path] = {
                        "code": codes[level],
                        "name": path[level],
                        "children": [],
                        "level": level,
                        "path": path[:level + 1]
                    }

    # 构建树形结构
    result = []

    # 按层级排序，先处理顶级节点
    sorted_paths = sorted(all_paths.items(), key=lambda x: x[1]["level"])

    for path_key, node_data in sorted_paths:
        if node_data["level"] == 0:
            # 顶级节点
            top_node = create_hierarchy_node(node_data["name"], node_data["code"])
            result.append(top_node)
        else:
            # 子节点，找到父节点并添加到其children中
            parent_path = " -> ".join(node_data["path"][:-1])
            if parent_path in all_paths:
                # 在结果中找到对应的父节点
                parent_node = find_node_in_tree(result, all_paths[parent_path]["path"])
                if parent_node is not None:
                    if "children" not in parent_node:
                        parent_node["children"] = []

                    child_node = create_hierarchy_node(node_data["name"], node_data["code"])
                    parent_node["children"].append(child_node)

    # 如果指定了输出文件路径，保存到文件
    if output_file_path:
        with open(output_file_path, 'w', encoding='utf-8') as output_file:
            json.dump(result, output_file, ensure_ascii=False, indent=2)
        print(f"JSON数据已保存到: {output_file_path}")

    return result


def find_node_in_tree(tree: List[Dict], target_path: List[str]) -> Optional[Dict]:
    """
    在树形结构中查找指定路径的节点

    Args:
        tree: 树形结构
        target_path: 目标路径

    Returns:
        找到的节点或None
    """
    if not target_path:
        return None

    # 查找顶级节点
    for node in tree:
        if node["name"] == target_path[0]:
            if len(target_path) == 1:
                return node
            else:
                # 递归查找子节点
                if "children" in node:
                    return find_node_in_children(node["children"], target_path[1:])

    return None


def find_node_in_children(children: List[Dict], target_path: List[str]) -> Optional[Dict]:
    """
    在子节点中查找指定路径的节点

    Args:
        children: 子节点列表
        target_path: 目标路径

    Returns:
        找到的节点或None
    """
    if not target_path:
        return None

    for child in children:
        if child["name"] == target_path[0]:
            if len(target_path) == 1:
                return child
            else:
                if "children" in child:
                    return find_node_in_children(child["children"], target_path[1:])

    return None


def main():
    """主函数"""
    csv_file = "/Users/<USER>/Desktop/classify/classify.csv"
    output_file = "/Users/<USER>/Desktop/classify/classify.json"
    
    try:
        print("开始转换CSV到JSON...")
        result = process_csv_to_json(csv_file, output_file)
        
        print(f"转换完成！共生成 {len(result)} 个顶级节点")
        
        # 打印前几个节点作为示例
        print("\n示例数据:")
        for i, node in enumerate(result[:3]):
            print(f"节点 {i+1}:")
            print(json.dumps(node, ensure_ascii=False, indent=2))
            print("-" * 50)
            
    except Exception as e:
        print(f"转换过程中出现错误: {e}")


if __name__ == "__main__":
    main()
