# CSV转JSON转换器

这个脚本可以将分层的CSV文件转换为嵌套的JSON格式。

## 功能特点

- 自动处理CSV文件中的层级继承关系
- 将中文名称转换为拼音首字母大写的代码
- 生成无限递归的嵌套JSON结构
- 支持自定义输出文件路径

## 输出格式

每个节点包含以下字段：
```json
{
   "code": "拼音首字母大写代码",
   "name": "中文名称",
   "children": [
      {
         "code": "子级代码",
         "name": "子级名称",
         "children": [
            // 无限递归...
         ]
      }
   ]
}
```

## 使用方法

### 1. 安装依赖
```bash
pip install pypinyin
```

### 2. 运行脚本
```bash
python csv_to_json_converter.py
```

### 3. 自定义使用
```python
from csv_to_json_converter import process_csv_to_json

# 转换CSV文件
result = process_csv_to_json('input.csv', 'output.json')

# 只获取数据不保存文件
result = process_csv_to_json('input.csv')
```

## 输入文件格式

CSV文件应包含5列：
- 第1列：导航栏
- 第2列：一级分类
- 第3列：二级分类
- 第4列：三级分类
- 第5列：四级分类

当某一行的前面列为空时，会自动继承上一行对应列的值。

## 示例

输入CSV：
```csv
导航栏,一级分类,二级分类,三级分类,四级分类
建华建材,预制桩生产设备类,自动化设备类,精密数控滚焊机,产品介绍
,,,,检测报告
,,,,用户手册
```

输出JSON：
```json
[
  {
    "code": "JHJCYZZSCSBLZDHSBLJMSKGHJCPJS",
    "name": "产品介绍",
    "parent": {
      "code": "JHJC",
      "name": "建华建材",
      "parent": {
        "code": "JHJCYZZSCSBL", 
        "name": "预制桩生产设备类",
        "parent": {
          "code": "JHJCYZZSCSBLZDHSBL",
          "name": "自动化设备类",
          "parent": {
            "code": "JHJCYZZSCSBLZDHSBLJMSKGHJ",
            "name": "精密数控滚焊机"
          }
        }
      }
    }
  }
]
```

## 文件说明

- `csv_to_json_converter.py` - 主转换脚本
- `classify.csv` - 输入的CSV文件
- `classify.json` - 输出的JSON文件
- `README.md` - 使用说明文档
